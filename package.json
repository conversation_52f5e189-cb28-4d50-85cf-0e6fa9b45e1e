{"name": "instagram-media-downloader", "version": "1.0.0", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yasinatesim/instagram-media-downloader"}, "engines": {"node": "18"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint --fix && tsc&& prettier --check --ignore-path .prettierignore .", "format": "prettier --write --ignore-unknown .", "prepare": "husky install"}, "dependencies": {"axios": "^1.6.7", "cheerio": "1.0.0-rc.12", "classnames": "^2.5.1", "firebase-admin": "^12.0.0", "instagram-private-api": "^1.45.3", "next": "14.0.4", "node-fetch": "^3.3.2", "react": "^18", "react-dom": "^18", "react-google-recaptcha-v3": "^1.10.1", "react-hot-toast": "^2.4.1", "sass": "^1.69.7"}, "devDependencies": {"@commitlint/cli": "^18.4.4", "@commitlint/config-conventional": "^18.4.4", "@svgr/webpack": "^8.1.0", "@types/classnames": "^2.3.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^10.0.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.2.1", "typescript": "^5"}}