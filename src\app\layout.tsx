import React from 'react';
import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';
import { Toaster } from 'react-hot-toast';

import type { Metadata } from 'next';
import { Inter } from 'next/font/google';

import GithubCorner from '@/components/GithubCorner';

import '@/assets/styles/globals.scss';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Instagram Media Downloader',
  description: 'Generated by create next app',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
        <Toaster position="top-center" reverseOrder={false} />
        <GithubCorner />
      </body>
    </html>
  );
}
